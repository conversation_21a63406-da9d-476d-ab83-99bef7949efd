package com.djcps.ai.core.vo.in.easyorder;

import lombok.Data;

@Data
public class TaskResultSearchBo  {
    /**
     * 搜索关键字
     */
    private String skey;
    private String supplierId;
    private Long taskId;
    private String orgId;
    private String role;
    private Integer pageNo=1;

    /**
     * 页记录大小
     */
    private Integer pageSize=200;
    /**
     * 前端忽略
     */
    private String userId;
    /**
     * 前端忽略
     */
    private String batchNo;
    /**
     * 前端忽略
     */
    private String dataDate;

    /**
     * 周期类型：day, week, month
     */
    private String cycleType;
    /**
     * 业务类型 交付:deliver,销售 sale
     */
    private String bizType;
}
