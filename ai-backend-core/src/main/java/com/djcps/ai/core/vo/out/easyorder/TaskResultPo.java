package com.djcps.ai.core.vo.out.easyorder;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务结果查询返回对象
 *
 * <AUTHOR> Assistant
 * @create 2025-08-05
 */
@Data
public class TaskResultPo {
    
    private Long id;
    
    private String skey;
    
    private String supplierId;
    
    private Long taskId;
    
    private String cycleType;
    
    private String orgId;
    
    private String role;
    
    private String userId;
    
    private String bizType;
    
    private String batchNo;
    
    private String dataDate;
    
    private LocalDateTime execStartTime;
    
    private LocalDateTime execEndTime;
    
    private LocalDateTime createTime;
    
    private Long isDel;
}
