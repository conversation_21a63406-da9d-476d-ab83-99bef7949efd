package com.djcps.ai.dao.dto;

import lombok.Data;

import java.util.Date;

@Data
public class TaskResultDto {
    private Long id;
    private String skey;
    private String supplierId;
    private Long taskId;
    private String cycleType;
    private String orgId;
    private String role;
    private String userId;
    private String bizType;
    private String batchNo;
    private String dataDate;
    /**
     * 是否保留同一周期的数据,0不保留,1保留
     */
    private Integer reten;
    private Date execStartTime;
    private Date execEndTime;
}
