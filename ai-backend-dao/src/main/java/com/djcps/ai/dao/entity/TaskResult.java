package com.djcps.ai.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.djcps.ai.dao.base.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("task_result")
public class TaskResult extends BaseEntity {
    private String skey;
    private Long taskId;
    private String cycleType;
    private String bizType;
    private String prompt;
    private String result;
    private String param;
    private String dataDate;
    private Date execStartTime;
    private Date execEndTime;
}
