package com.djcps.ai.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.core.client.EasyOrderFeign;
import com.djcps.ai.core.vo.in.easyorder.TaskResultItemSaveBo;
import com.djcps.ai.core.vo.in.easyorder.TaskResultSaveBo;
import com.djcps.ai.dao.entity.TaskResult;
import com.djcps.ai.dao.mapper.TaskResultMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class TaskResultOldService extends ServiceImpl<TaskResultMapper, TaskResult> {

    private final EasyOrderFeign easyOrderFeign;
    public void oldSync() {
        log.info("开始执行oldSync方法，从task_result表同步数据");
        
        // 分页配置
        final int PAGE_SIZE = 50;
        int totalSuccessCount = 0;
        int totalFailCount = 0;
        
        // 1. 先查询出所有需要处理的数据ID，避免在处理过程中查询条件动态变化
        LambdaQueryWrapper<TaskResult> idQueryWrapper = new LambdaQueryWrapper<>();
        idQueryWrapper.isNull(TaskResult::getParam)
                     .eq(TaskResult::getIsDel, 0)
                     .select(TaskResult::getId)
                     .orderByAsc(TaskResult::getId);
        
        List<TaskResult> allTaskResults = this.list(idQueryWrapper);
        if (CollUtil.isEmpty(allTaskResults)) {
            log.info("未找到需要同步的task_result数据");
            return;
        }
        
        List<Long> allTaskResultIds = allTaskResults.stream()
                .map(TaskResult::getId)
                .collect(java.util.stream.Collectors.toList());
        
        log.info("找到{}条需要同步的数据，准备分页处理", allTaskResultIds.size());
        
        // 2. 分页处理固定的ID列表
        int totalCount = allTaskResultIds.size();
        int totalPages = (totalCount + PAGE_SIZE - 1) / PAGE_SIZE;
        
        for (int currentPage = 1; currentPage <= totalPages; currentPage++) {
            // 计算当前页的ID范围
            int startIndex = (currentPage - 1) * PAGE_SIZE;
            int endIndex = Math.min(startIndex + PAGE_SIZE, totalCount);
            List<Long> currentPageIds = allTaskResultIds.subList(startIndex, endIndex);
            
            // 根据ID查询当前页的完整数据
            LambdaQueryWrapper<TaskResult> pageQueryWrapper = new LambdaQueryWrapper<>();
            pageQueryWrapper.in(TaskResult::getId, currentPageIds)
                           .orderByAsc(TaskResult::getId);
            
            List<TaskResult> taskResults = this.list(pageQueryWrapper);
            
            if (CollUtil.isEmpty(taskResults)) {
                log.warn("第{}页根据ID查询无数据，跳过处理", currentPage);
                continue;
            }
            
            log.info("处理第{}/{}页数据，共{}条记录", currentPage, totalPages, taskResults.size());
            
            int pageSuccessCount = 0;
            int pageFailCount = 0;
            
            // 处理当前页的数据
            for (TaskResult taskResult : taskResults) {
                String batchNo = IdUtil.simpleUUID();
                try {
                    // 保存TaskResult到远程系统
                    Integer savedTaskResultId = saveTaskResultToRemote(taskResult, batchNo);
                    if (savedTaskResultId != null) {
                        // 如果TaskResult的result字段有值，则保存到TaskResultItem
                        if (StrUtil.isNotBlank(taskResult.getResult())) {
                            saveTaskResultItemToRemote(taskResult, savedTaskResultId, batchNo);
                        }
                        
                        // 同步成功后，更新本地TaskResult的param字段为batchNo
                        updateTaskResultParam(taskResult.getId(), batchNo);
                        
                        pageSuccessCount++;
                        log.debug("成功同步TaskResult，ID: {}, 远程ID: {}, batchNo: {}", 
                                 taskResult.getId(), savedTaskResultId, batchNo);
                    } else {
                        pageFailCount++;
                        log.error("保存TaskResult失败，ID: {}", taskResult.getId());
                    }
                } catch (Exception e) {
                    pageFailCount++;
                    log.error("同步TaskResult失败，ID: {}, batchNo: {}", taskResult.getId(), batchNo, e);
                }
            }
            
            totalSuccessCount += pageSuccessCount;
            totalFailCount += pageFailCount;
            
            log.info("第{}/{}页处理完成，成功{}条，失败{}条", currentPage, totalPages, pageSuccessCount, pageFailCount);
        }
        
        log.info("oldSync方法执行完成，总共处理{}条记录，成功{}条，失败{}条", 
                 totalCount, totalSuccessCount, totalFailCount);
    }
    /**
     * 保存TaskResult到远程系统
     */
    private Integer saveTaskResultToRemote(TaskResult taskResult, String batchNo) {
        try {
            TaskResultSaveBo saveBo = new TaskResultSaveBo();
            BeanUtil.copyProperties(taskResult, saveBo);
            saveBo.setBatchNo(batchNo);
            
            // 转换Date到LocalDateTime
            if (taskResult.getExecStartTime() != null) {
                saveBo.setExecStartTime(taskResult.getExecStartTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime());
            }
            if (taskResult.getExecEndTime() != null) {
                saveBo.setExecEndTime(taskResult.getExecEndTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime());
            }
            
            // 调用远程接口保存
            WebResultExt<Integer> result = easyOrderFeign.saveTaskResult(saveBo);
            if (result.isSuccess()) {
                log.debug("远程保存TaskResult成功，返回ID: {}", result.getData());
                return result.getData();
            } else {
                log.error("远程保存TaskResult失败: {}", result.getMsg());
                return null;
            }
        } catch (Exception e) {
            log.error("调用远程保存TaskResult接口异常", e);
            return null;
        }
    }
    
    /**
     * 保存TaskResultItem到远程系统
     */
    private void saveTaskResultItemToRemote(TaskResult taskResult, Integer resultId, String batchNo) {
        try {
            TaskResultItemSaveBo saveBo = new TaskResultItemSaveBo();
            saveBo.setResultId(resultId);
            saveBo.setTaskId(taskResult.getTaskId().intValue());
            String itemResult = taskResult.getResult();
            saveBo.setOriginData(itemResult); // 使用result字段作为原始数据
            saveBo.setItemResult(itemResult);
            saveBo.setSkey(taskResult.getSkey());
            saveBo.setSubBizType("summary"); // 固定设置为summary
            saveBo.setBatchNo(batchNo);
            saveBo.setPrompt(taskResult.getPrompt());
            String cycleType = taskResult.getCycleType();
            Integer templateIdByCycle = getTemplateIdByCycle(cycleType);
            saveBo.setTemplateId(templateIdByCycle);

            // 调用远程接口保存
            WebResultExt<Long> result = easyOrderFeign.saveTaskResultItem(saveBo);
            if (result.isSuccess()) {
                log.debug("远程保存TaskResultItem成功，返回ID: {}", result.getData());
            } else {
                log.error("远程保存TaskResultItem失败: {}", result.getMsg());
                throw new RuntimeException("保存TaskResultItem失败: " + result.getMsg());
            }
        } catch (Exception e) {
            log.error("调用远程保存TaskResultItem接口异常", e);
            throw new RuntimeException("保存TaskResultItem失败", e);
        }
    }

    /**
     * 更新TaskResult的param字段
     */
    private void updateTaskResultParam(Long taskResultId, String batchNo) {
        try {
            LambdaUpdateWrapper<TaskResult> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TaskResult::getId, taskResultId)
                        .set(TaskResult::getParam, batchNo);
            
            boolean updateResult = this.update(updateWrapper);
            if (updateResult) {
                log.debug("成功更新TaskResult的param字段，ID: {}, batchNo: {}", taskResultId, batchNo);
            } else {
                log.error("更新TaskResult的param字段失败，ID: {}, batchNo: {}", taskResultId, batchNo);
            }
        } catch (Exception e) {
            log.error("更新TaskResult的param字段异常，ID: {}, batchNo: {}", taskResultId, batchNo, e);
            throw new RuntimeException("更新param字段失败", e);
        }
    }

    public static Integer getTemplateIdByCycle(String cycle) {
        if (StrUtil.equalsIgnoreCase(cycle,"day")) {
            return 1;
        }
        if (StrUtil.equalsIgnoreCase(cycle,"week")) {
            return 2;
        }
        if (StrUtil.equalsIgnoreCase(cycle,"month")) {
            return 3;
        }
        return 4;
    }
}
