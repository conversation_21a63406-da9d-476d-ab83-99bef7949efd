package com.djcps.ai.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.djcps.ai.common.result.WebResultExt;
import com.djcps.ai.core.client.EasyOrderFeign;
import com.djcps.ai.core.vo.in.easyorder.*;
import com.djcps.ai.core.vo.out.easyorder.TaskResultItemQueryVo;
import com.djcps.ai.core.vo.out.easyorder.TaskResultPo;
import com.djcps.ai.dao.dto.TaskResultDto;
import com.djcps.ai.dao.dto.TaskResultItem;
import com.djcps.ai.dao.entity.TaskInvokeMethod;
import com.djcps.ai.dao.entity.UserTaskRunRecord;
import com.djcps.ai.dao.entity.UserTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class TaskResultService {
    private final TaskInvokeMethodService taskInvokeMethodService;
    private final UserTaskRunRecordService userTaskRunRecordService;
    private final UserTemplateService templateService;
    private final EasyOrderFeign easyOrderFeign;

    /**
     * 根据任务ID和批次号查询结果项
     *
     * @param taskId  任务ID
     * @param batchNo 批次号
     * @return 结果项列表
     */
    public List<TaskResultItem> getByTaskIdAndBatchNo(Long taskId, String batchNo) {
        try {
            TaskResultItemQueryBo queryBo = new TaskResultItemQueryBo();
            queryBo.setTaskId(taskId);
            queryBo.setBatchNo(batchNo);

            List<TaskResultItemQueryVo> voList = easyOrderFeign.getTaskResultItems(queryBo).fetchPageDataList();
            if (voList == null || voList.isEmpty()) {
                log.info("远程查询任务结果项返回空列表，taskId: {}, batchNo: {}", taskId, batchNo);
                return List.of();
            }

            // 转换VO到Entity
            List<TaskResultItem> entityList = voList.stream()
                    .map(vo -> {
                        TaskResultItem entity = new TaskResultItem();
                        BeanUtil.copyProperties(vo, entity);
                        return entity;
                    })
                    .collect(Collectors.toList());

            log.info("远程查询任务结果项成功，返回 {} 条记录", entityList.size());
            return entityList;
        } catch (Exception e) {
            log.error("调用远程查询任务结果项接口失败，taskId: {}, batchNo: {}", taskId, batchNo, e);
            throw new RuntimeException("调用远程查询任务结果项接口失败: " + e.getMessage(), e);
        }
    }


    public Long saveTask(TaskResultDto taskResult) {
        try {
            TaskResultSaveBo saveBo = new TaskResultSaveBo();
            BeanUtil.copyProperties(taskResult, saveBo);

            // 转换Date到LocalDateTime
            if (taskResult.getExecStartTime() != null) {
                saveBo.setExecStartTime(taskResult.getExecStartTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime());
            }
            if (taskResult.getExecEndTime() != null) {
                saveBo.setExecEndTime(taskResult.getExecEndTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime());
            }

            WebResultExt<Integer> result = easyOrderFeign.saveTaskResult(saveBo);
            if (!result.isSuccess()) {
                log.error("远程保存任务结果失败: {}", result.getMsg());
                throw new RuntimeException("保存任务结果失败: " + result.getMsg());
            }
            Long data = Long.valueOf(result.getData());
            log.info("远程保存任务结果成功，返回ID: {}", data);
            return data;
        } catch (Exception e) {
            log.error("调用远程保存任务结果接口失败", e);
            throw new RuntimeException("调用远程保存任务结果接口失败: " + e.getMessage(), e);
        }
    }
    public Long saveResultItem(TaskResultDto taskResult, UserTemplate template, String batchNo, String originData) {
        try {
            TaskResultItemSaveBo saveBo = new TaskResultItemSaveBo();
            saveBo.setResultId(taskResult.getId().intValue());
            saveBo.setTaskId(taskResult.getTaskId().intValue());
            saveBo.setTemplateId(template.getId().intValue());
            saveBo.setOriginData(originData);
            saveBo.setSkey(taskResult.getSkey());
            saveBo.setPrompt(template.getPrompt());
            saveBo.setSubBizType(template.getSubBizType());
            saveBo.setBatchNo(batchNo);

            WebResultExt<Long> result = easyOrderFeign.saveTaskResultItem(saveBo);
            if (!result.isSuccess()) {
                log.error("远程保存任务结果项失败: {}", result.getMsg());
                throw new RuntimeException("保存任务结果项失败: " + result.getMsg());
            }
            log.info("远程保存任务结果项成功，返回ID: {}", result.getData());
            return result.getData();
        } catch (Exception e) {
            log.error("调用远程保存任务结果项接口失败", e);
            throw new RuntimeException("调用远程保存任务结果项接口失败: " + e.getMessage(), e);
        }
    }

    public void updateResultItem(Long taskItemId, String analysisResult,String prompt) {
        try {
            TaskResultItemUpdateBo updateBo = new TaskResultItemUpdateBo();
            updateBo.setTaskItemId(taskItemId);
            updateBo.setAnalysisResult(analysisResult);
            updateBo.setPrompt(prompt);

            WebResultExt<String> result = easyOrderFeign.updateTaskResultItem(updateBo);
            if (!result.isSuccess()) {
                log.error("远程更新任务结果项失败: {}", result.getMsg());
                throw new RuntimeException("更新任务结果项失败: " + result.getMsg());
            }
            log.info("远程更新任务结果项成功: {}", result.getData());
        } catch (Exception e) {
            log.error("调用远程更新任务结果项接口失败", e);
            throw new RuntimeException("调用远程更新任务结果项接口失败: " + e.getMessage(), e);
        }
    }


    /**
     * 根据批次号重新分析任务结果
     * 根据已有的origin_data重新调用分析功能，更新item_result
     *
     * @param taskId  任务ID
     * @param batchNo 批次号
     * @return 重新分析的结果项数量
     */
    public int reanalyzeTaskByBatch(Long taskId, String batchNo) {
        log.info("开始重新分析任务，taskId: {}, batchNo: {}", taskId, batchNo);

        // 获取该批次的所有结果项
        List<TaskResultItem> resultItems = getByTaskIdAndBatchNo(taskId, batchNo);
        if (resultItems.isEmpty()) {
            log.warn("任务 {} 批次 {} 没有找到相关的结果项", taskId, batchNo);
            return 0;
        }


        // 创建重新分析的执行记录
        Long runRecordId = userTaskRunRecordService.createRunRecord(taskId, batchNo,
                UserTaskRunRecord.ExecType.REANALYZE);
        TaskInvokeMethod analysisMethod = taskInvokeMethodService.getAnalysisMethod();
        int successCount = 0;
        List<UserTemplate> templates = templateService.listByIds(resultItems.stream().map(TaskResultItem::getTemplateId).collect(Collectors.toSet()));
        //templates 收集为 map id,template
        if (templates.isEmpty()) {
            log.warn("任务 {} 批次 {} 没有找到相关的模板", taskId, batchNo);
            userTaskRunRecordService.updateRecordFailed(runRecordId, "没有找到相关的模板");
            return 0;
        }
        Map<Long, UserTemplate> temMap = templates.stream()
                .collect(Collectors.toMap(UserTemplate::getId, Function.identity()));

        //templateId,  method中的output_scheme
        List<TaskInvokeMethod> taskInvokeMethodList = taskInvokeMethodService.listByIds(templates.stream().map(UserTemplate::getMethodId).collect(Collectors.toSet()));
        Map<Long, String> templateOutputMap = taskInvokeMethodList.stream()
                .collect(Collectors.toMap(TaskInvokeMethod::getId, TaskInvokeMethod::getOutputScheme));
        try {
            for (TaskResultItem item : resultItems) {
                try {
                    // 检查是否有原始数据
                    if (item.getOriginData() == null || item.getOriginData().trim().isEmpty()) {
                        log.warn("结果项 {} 没有原始数据，跳过重新分析", item.getId());
                        continue;
                    }
                    Long templateId = item.getTemplateId();
                    UserTemplate template = temMap.get(templateId);
                    if (template == null) {
                        log.warn("结果项 {} 对应的模板 {} 不存在，跳过重新分析", item.getId(), templateId);
                        continue;
                    }
                    Long methodId = template.getMethodId();
                    String scheme = templateOutputMap.getOrDefault(methodId, "");

                    // 重新调用分析方法
                    String prompt = template.getPrompt();
                    String analysisResult = taskInvokeMethodService.invokeAnalysisMethod(
                            taskId,
                            batchNo,
                            item.getSkey(),
                            analysisMethod,
                            prompt,
                            item.getOriginData(),
                            scheme
                    );

                    // 更新分析结果
                    if (analysisResult != null) {
                        updateResultItem(item.getId(), analysisResult,prompt);
                        successCount++;
                        log.debug("成功重新分析结果项 {}", item.getId());
                    } else {
                        log.warn("结果项 {} 重新分析返回空结果", item.getId());
                    }

                } catch (Exception e) {
                    log.error("重新分析结果项 {} 失败", item.getId(), e);
                }
            }

            // 更新执行记录为成功状态
            userTaskRunRecordService.updateRecordSuccess(runRecordId, successCount);

            log.info("任务 {} 重新分析完成，成功处理 {} 个结果项，总共 {} 个结果项",
                    taskId, successCount, resultItems.size());
            return successCount;
        } catch (Exception e) {
            log.error("重新分析任务 {} 失败", taskId, e);
            // 更新执行记录为失败状态
            userTaskRunRecordService.updateRecordFailed(runRecordId, e.getMessage());
            throw e;
        }
    }

    /**
     * 获取任务结果详情
     *
     * @param taskId  任务ID
     * @param batchNo 批次号
     * @return 任务结果详情
     */
    public Map<String, Object> getTaskResultDetails(Long taskId, String batchNo) {
        Map<String, Object> result = new HashMap<>();

        // 获取TaskResult列表 - 使用远程调用
        List<TaskResultDto> taskResults;
        try {
            TaskResultSearchBo searchBo = new TaskResultSearchBo();
            searchBo.setTaskId(taskId);
            searchBo.setBatchNo(batchNo);

            List<TaskResultPo> objects = easyOrderFeign.pageCommon(searchBo).fetchPageDataList();
            if (CollUtil.isEmpty(objects)) {
                log.info("远程查询任务结果返回空列表，taskId: {}, batchNo: {}", taskId, batchNo);
                taskResults = List.of();
            } else {
                // 转换Po到Entity
                taskResults = objects.stream()
                        .map(po -> {
                            TaskResultDto entity = new TaskResultDto();
                            BeanUtil.copyProperties(po, entity);
                            return entity;
                        })
                        .collect(Collectors.toList());
                log.info("远程查询任务结果成功，返回 {} 条记录", taskResults.size());
            }
        } catch (Exception e) {
            log.error("调用远程查询任务结果接口失败，taskId: {}, batchNo: {}", taskId, batchNo, e);
            throw new RuntimeException("调用远程查询任务结果接口失败: " + e.getMessage(), e);
        }

        // 获取TaskResultItem列表
        List<TaskResultItem> taskResultItems = getByTaskIdAndBatchNo(taskId, batchNo);

        // 按sub_biz_type分组TaskResultItem
        Map<String, List<TaskResultItem>> itemsBySubBizType = taskResultItems.stream()
                .collect(Collectors.groupingBy(TaskResultItem::getSubBizType));

        result.put("taskResults", taskResults);
        result.put("taskResultItems", taskResultItems);
        result.put("itemsBySubBizType", itemsBySubBizType);

        return result;
    }
}
