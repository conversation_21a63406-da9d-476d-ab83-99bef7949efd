package com.djcps.ai.service;

import com.djcps.ai.dao.entity.TaskInvokeMethod;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserTaskAnalysisDataService {

    private final TaskResultService taskResultService;
    private final TaskInvokeMethodService methodService;


    /**
     * 异步执行分析任务
     *
     * @param taskItemId 任务项ID
     * @param taskId 任务ID
     * @param batchNo 批次号
     * @param skey 业务键
     * @param analysisMethod 分析方法
     * @param prompt 提示词
     * @param originData 原始数据
     * @param outputScheme 输出方案
     */
    @Async("analysisTaskExec")
    public void executeAnalysisAsync(Long taskItemId, Long taskId, String batchNo,
                                                        String skey, TaskInvokeMethod analysisMethod,
                                                        String prompt, String originData, String outputScheme) {
        try {
            log.info("executeAnalysisAsync executing in thread: {}", Thread.currentThread().getName());
            log.debug("开始异步分析任务项: {}", taskItemId);
            String analysisResult = methodService.invokeAnalysisMethod(taskId, batchNo + "_" + taskItemId,
                                                                      skey, analysisMethod, prompt, originData, outputScheme);
            taskResultService.updateResultItem(taskItemId, analysisResult, prompt);
            log.debug("完成异步分析任务项: {}", taskItemId);
        } catch (Exception e) {
            log.error("异步分析任务项 {} 失败", taskItemId, e);
        }
    }


}
