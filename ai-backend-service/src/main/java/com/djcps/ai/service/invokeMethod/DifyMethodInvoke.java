package com.djcps.ai.service.invokeMethod;

import cn.hutool.core.util.StrUtil;
import com.djcps.ai.core.client.DifyWorkflowFeignClient;
import com.djcps.ai.core.constants.CommonConstants;
import com.djcps.ai.core.param.dify.DifyWordflowRequestParam;
import com.djcps.ai.core.vo.DifyWorkflowResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("dify")
@RequiredArgsConstructor
@Slf4j
public class DifyMethodInvoke implements InvokeService{
    private final DifyWorkflowFeignClient difyWorkflowFeignClient;
    private final static String USER_ID_TEMP = "{}_{}_{}_{}";
    private final static int MAX_RETRY_ATTEMPTS = 4;
    private final static long INITIAL_RETRY_DELAY_MS = 100L;

    @Override
    public Object invoke(InvokeParam invokeParam) {
        DifyWordflowRequestParam param = new DifyWordflowRequestParam();
        param.setUser(StrUtil.format(USER_ID_TEMP,invokeParam.getBatchNo(),invokeParam.getSkey(),invokeParam.getTaskId()));
        param.setInputs(invokeParam.getMergeParams());
        
        return invokeWithRetry(param, invokeParam.getConfig());
    }

    private Object invokeWithRetry(DifyWordflowRequestParam param, String config) {
        DifyWorkflowResponse lastResponse = null;
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                log.debug("Dify workflow invocation attempt {} of {}", attempt, MAX_RETRY_ATTEMPTS);
                
                DifyWorkflowResponse response = difyWorkflowFeignClient.run(param, CommonConstants.buildHeaderAuthorizationBear(config));
                
                // Check if the response is successful
                if (isSuccessfulResponse(response)) {
                    if (attempt > 1) {
                        log.info("Dify workflow succeeded on attempt {} after {} retries", attempt, attempt - 1);
                    }
                    return response.getData().getOutputs().getResult();
                }
                
                // Response received but status indicates failure
                lastResponse = response;
                log.warn("Dify workflow attempt {} failed with status: {}, error: {}", 
                    attempt, 
                    response.getData() != null ? response.getData().getStatus() : "unknown",
                    response.getData() != null ? response.getData().getError() : "no error details");
                
            } catch (Exception e) {
                lastException = e;
                log.warn("Dify workflow attempt {} failed with exception: {}", attempt, e.getMessage());
            }
            
            // If this wasn't the last attempt, wait before retrying
            if (attempt < MAX_RETRY_ATTEMPTS) {
                try {
                    long delayMs = INITIAL_RETRY_DELAY_MS * (long) Math.pow(2, attempt - 1);
                    log.debug("Waiting {}ms before retry attempt {}", delayMs, attempt + 1);
                    Thread.sleep(delayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("Retry delay interrupted", ie);
                    break;
                }
            }
        }
        
        // All attempts failed
        if (lastResponse != null) {
            log.error("Dify workflow failed after {} attempts. Final response: {}", MAX_RETRY_ATTEMPTS, lastResponse);
        } else if (lastException != null) {
            log.error("Dify workflow failed after {} attempts with exception", MAX_RETRY_ATTEMPTS, lastException);
        }
        
        return null;
    }
    
    private boolean isSuccessfulResponse(DifyWorkflowResponse response) {
        return response != null 
            && response.getData() != null 
            && StrUtil.containsIgnoreCase(response.getData().getStatus(), "succeeded");
    }

}
