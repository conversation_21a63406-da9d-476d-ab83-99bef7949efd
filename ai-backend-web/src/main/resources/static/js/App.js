// App.js - 主应用组件
const { useState, useEffect } = React;

function App() {
    // 从 localStorage 获取上次保存的页面
    const getStoredActiveNav = () => {
        try {
            return localStorage.getItem('app_activeNav') || 'taskResults';
        } catch (e) {
            console.warn('无法从 localStorage 读取 activeNav:', e);
            return 'taskResults';
        }
    };

    // 保存当前页面到 localStorage
    const saveActiveNavToStorage = (nav) => {
        try {
            localStorage.setItem('app_activeNav', nav);
        } catch (e) {
            console.warn('无法保存 activeNav 到 localStorage:', e);
        }
    };

    // 当前选中的导航项
    const [activeNav, setActiveNav] = useState(getStoredActiveNav());

    // 监听导航变化，保存到 localStorage
    useEffect(() => {
        saveActiveNavToStorage(activeNav);
    }, [activeNav]);

    // 根据activeNav渲染对应的组件
    const renderContent = () => {
        switch(activeNav) {
            case 'template':
                return <TemplateManagement />;
            case 'taskCrud':
                return <TaskManagementCrud />;
            case 'taskResults':
                return <TaskResults />;
            case 'taskInvokeMethod':
                return <TaskInvokeMethodManagement />;
            default:
                return <TaskResults />;
        }
    };

    return (
        <div className="flex h-screen overflow-hidden">
            {/* 左侧导航栏 */}
            <Sidebar activeNav={activeNav} setActiveNav={setActiveNav} />
            
            {/* 主内容区域 */}
            <main className="flex-1 overflow-y-auto p-6">
                <div className="glass rounded-xl p-6 h-full">
                    {renderContent()}
                </div>
            </main>
        </div>
    );
}

// 渲染App组件到DOM
ReactDOM.createRoot(document.getElementById('root')).render(<App />);