// TaskManagementCrud.js - 任务增删改查管理组件
const { useState, useEffect } = React;

function TaskManagementCrud() {
    // 状态管理
    const [tasks, setTasks] = useState([]);
    const [templates, setTemplates] = useState([]);
    const [loading, setLoading] = useState(true);
    const [templatesLoading, setTemplatesLoading] = useState(false);
    const [selectedTask, setSelectedTask] = useState(null);
    const [isEditing, setIsEditing] = useState(false);
    const [formData, setFormData] = useState({
        title: '',
        bizType: '',
        templateId: '',
        cornExp: '0 0 9 * * ?',
        cycleType: 'day',
        methodId: '',
        reten: 0,
        coverRepeat: 0
    });

    // 工具数据相关状态
    const [toolsData, setToolsData] = useState([]);
    const [toolsLoading, setToolsLoading] = useState(false);
    const [selectedMethodId, setSelectedMethodId] = useState('');
    const [selectedTemplateIds, setSelectedTemplateIds] = useState([]);

    // Cron表达式校验状态
    const [cronValidationError, setCronValidationError] = useState('');

    // 获取任务列表
    useEffect(() => {
        fetchTaskList();
        fetchToolsList();
    }, []);

    // 从后端获取任务列表
    const fetchTaskList = async () => {
        try {
            setLoading(true);
            const result = await apiRequest(API_CONFIG.TASK.LIST);

            if (result.success && result.data) {
                const mappedTasks = result.data.map(task => ({
                    id: task.id,
                    title: task.title || '',
                    bizType: task.bizType || '',
                    templateId: task.templateId || '',
                    cornExp: task.cornExp || '',
                    cycleType: task.cycleType || '',
                    methodId: task.methodId || '',
                    reten: task.reten || 0,
                    coverRepeat: task.coverRepeat || 0,
                    userId: task.userId || '',
                    createdAt: task.createTime ? new Date(task.createTime).toLocaleDateString() : '',
                    updatedAt: task.createTime ? new Date(task.createTime).toLocaleDateString() : ''
                }));

                setTasks(mappedTasks);
            } else {
                console.error('Invalid response format:', result);
                setTasks([]);
            }
        } catch (err) {
            console.error('Error fetching task list:', err);
            setTasks([]);
        } finally {
            setLoading(false);
        }
    };

    // 获取工具列表
    const fetchToolsList = async () => {
        try {
            setToolsLoading(true);
            const requestData = {
                pageNum: 1,
                pageSize: 1000,
                name: '',
                invokeType: '',
                useType: 'parameter'
            };

            const result = await apiRequest(API_CONFIG.TASK_INVOKE_METHOD.LIST, {
                method: 'POST',
                body: JSON.stringify(requestData)
            });

            if (result.success && result.data) {
                setToolsData(result.data.list || []);
            } else {
                console.error('获取工具列表失败:', result.msg);
                setToolsData([]);
            }
        } catch (err) {
            console.error('Error fetching tools list:', err);
            setToolsData([]);
        } finally {
            setToolsLoading(false);
        }
    };

    // 根据业务类型获取模板列表
    const fetchTemplatesByBizType = async (bizType) => {
        if (!bizType) {
            setTemplates([]);
            return;
        }

        try {
            setTemplatesLoading(true);
            const requestBody = { bizType };
            const result = await apiRequest(API_CONFIG.TEMPLATE.LIST, {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            if (result.success && result.data) {
                setTemplates(result.data);
            } else {
                console.error('获取模板列表失败:', result.msg);
                setTemplates([]);
            }
        } catch (err) {
            console.error('Error fetching templates:', err);
            setTemplates([]);
        } finally {
            setTemplatesLoading(false);
        }
    };

    // 处理业务类型变化
    const handleBizTypeChange = (bizType) => {
        setFormData(prev => ({ ...prev, bizType }));
        setSelectedTemplateIds([]);
        fetchTemplatesByBizType(bizType);
    };

    // 处理模板选择变化
    const handleTemplateChange = (templateId) => {
        setSelectedTemplateIds(prev => {
            if (prev.includes(templateId)) {
                return prev.filter(id => id !== templateId);
            } else {
                return [...prev, templateId];
            }
        });
    };

    // 处理工具选择变化
    const handleMethodChange = (methodId) => {
        setSelectedMethodId(methodId);
    };

    // 选择任务
    const handleSelectTask = (task) => {
        setSelectedTask(task);
        setFormData({
            title: task.title,
            bizType: task.bizType,
            templateId: task.templateId,
            cornExp: task.cornExp,
            cycleType: task.cycleType,
            methodId: task.methodId,
            reten: task.reten,
            coverRepeat: task.coverRepeat
        });

        // 设置选中的模板ID列表
        const templateIds = task.templateId ? task.templateId.split(',').map(id => id.trim()).filter(id => id) : [];
        setSelectedTemplateIds(templateIds);
        setSelectedMethodId(task.methodId || '');

        // 根据业务类型加载模板
        if (task.bizType) {
            fetchTemplatesByBizType(task.bizType);
        }

        setIsEditing(false);
    };

    // 复制任务
    const handleCopyTask = (task, event) => {
        // 阻止事件冒泡，避免触发选中任务的逻辑
        if (event) {
            event.stopPropagation();
        }
        
        setFormData({
            title: task.title + ' - 副本',
            bizType: task.bizType,
            templateId: task.templateId,
            cornExp: task.cornExp,
            cycleType: task.cycleType,
            methodId: task.methodId,
            reten: task.reten,
            coverRepeat: task.coverRepeat
        });
        
        // 设置选中的模板ID列表
        const templateIds = task.templateId ? task.templateId.split(',').map(id => id.trim()).filter(id => id) : [];
        setSelectedTemplateIds(templateIds);
        setSelectedMethodId(task.methodId || '');
        
        // 根据业务类型加载模板
        if (task.bizType) {
            fetchTemplatesByBizType(task.bizType);
        }
        
        setSelectedTask(null); // 清空选中状态，表示这是新建
        setIsEditing(true);
        setCronValidationError('');
    };

    // 创建新任务
    const handleCreateNew = () => {
        setFormData({
            title: '',
            bizType: '',
            templateId: '',
            cornExp: '0 0 9 * * ?',
            cycleType: 'day',
            methodId: '',
            reten: 0,
            coverRepeat: 0
        });
        setSelectedTask(null);
        setSelectedTemplateIds([]);
        setSelectedMethodId('');
        setTemplates([]);
        setIsEditing(true);
        setCronValidationError('');
    };

    // 编辑任务
    const handleEdit = () => {
        if (!selectedTask) return;
        setIsEditing(true);
        setCronValidationError('');
    };

    // 取消编辑
    const handleCancel = () => {
        setIsEditing(false);
        setCronValidationError('');
        if (selectedTask) {
            setFormData({
                title: selectedTask.title,
                bizType: selectedTask.bizType,
                templateId: selectedTask.templateId,
                cornExp: selectedTask.cornExp,
                cycleType: selectedTask.cycleType,
                methodId: selectedTask.methodId,
                reten: selectedTask.reten,
                coverRepeat: selectedTask.coverRepeat
            });
            const templateIds = selectedTask.templateId ? selectedTask.templateId.split(',').map(id => id.trim()).filter(id => id) : [];
            setSelectedTemplateIds(templateIds);
            setSelectedMethodId(selectedTask.methodId || '');
        }
    };

    // 处理表单输入变化
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // 如果是Cron表达式，进行实时校验
        if (name === 'cornExp') {
            const error = validateCronExpression(value);
            setCronValidationError(error || '');
        }
    };

    // 保存任务
    const handleSave = async () => {
        // 验证表单
        if (!formData.title.trim()) {
            window.showMessage.warning('任务标题不能为空');
            return;
        }

        if (!formData.bizType) {
            window.showMessage.warning('请选择业务类型');
            return;
        }

        if (selectedTemplateIds.length === 0) {
            window.showMessage.warning('请至少选择一个模板');
            return;
        }

        if (!formData.cornExp.trim()) {
            window.showMessage.warning('Cron表达式不能为空');
            return;
        }

        // 校验Cron表达式
        const cronError = validateCronExpression(formData.cornExp);
        if (cronError) {
            setCronValidationError(cronError);
            window.showMessage.warning('Cron表达式格式错误: ' + cronError);
            return;
        }

        try {
            const taskData = {
                id: selectedTask ? selectedTask.id : null,
                title: formData.title,
                bizType: formData.bizType,
                templateId: selectedTemplateIds.join(','),
                cornExp: formData.cornExp,
                cycleType: formData.cycleType,
                methodId: selectedMethodId || '',
                reten: parseInt(formData.reten),
                coverRepeat: parseInt(formData.coverRepeat)
            };

            const result = await apiRequest(API_CONFIG.TASK.SAVE, {
                body: JSON.stringify(taskData)
            });

            if (result.success && result.data) {
                const savedTask = {
                    id: result.data.id,
                    title: result.data.title || '',
                    bizType: result.data.bizType || '',
                    templateId: result.data.templateId || '',
                    cornExp: result.data.cornExp || '',
                    cycleType: result.data.cycleType || '',
                    methodId: result.data.methodId || '',
                    reten: result.data.reten || 0,
                    coverRepeat: result.data.coverRepeat || 0,
                    userId: result.data.userId || '',
                    createdAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : '',
                    updatedAt: result.data.createTime ? new Date(result.data.createTime).toLocaleDateString() : ''
                };

                setSelectedTask(savedTask);
                const templateIds = savedTask.templateId ? savedTask.templateId.split(',').map(id => id.trim()).filter(id => id) : [];
                setSelectedTemplateIds(templateIds);
                setIsEditing(false);
                window.showMessage.success('任务保存成功！');
            } else {
                throw new Error(result.msg || '保存失败');
            }
        } catch (error) {
            console.error('保存任务失败:', error);
            window.showMessage.error('保存任务失败: ' + error.message);
        } finally {
            // 无论成功或失败，都刷新任务列表
            await fetchTaskList();
        }
    };

    // 删除任务
    const handleDelete = async () => {
        if (!selectedTask) return;

        const confirmed = await window.showConfirm(`确定要删除任务 "${selectedTask.title}" 吗？`);
        if (confirmed) {
            try {
                const result = await apiRequest(API_CONFIG.TASK.DELETE, {}, { id: selectedTask.id });

                if (result.success !== false) {
                    await fetchTaskList();
                    setSelectedTask(null);
                    window.showMessage.success('任务删除成功！');
                } else {
                    throw new Error(result.msg || '删除失败');
                }
            } catch (error) {
                console.error('删除任务失败:', error);
                window.showMessage.error('删除任务失败: ' + error.message);
            }
        }
    };

    // 获取模板名称（支持多个模板ID，逗号分隔）
    const getTemplateName = (templateId) => {
        if (!templateId) return '未选择模板';

        const templateIds = templateId.split(',').map(id => id.trim()).filter(id => id);
        const templateNames = templateIds.map(id => {
            const template = templates.find(t => t.id.toString() === id);
            return template ? template.title : `未知模板(${id})`;
        });

        return templateNames.join(', ');
    };

    // 常用Cron表达式预设
    const cronPresets = [
        { label: '每天9点', value: '0 0 9 * * ?' },
        { label: '每天18点', value: '0 0 18 * * ?' },
        { label: '每周一9点', value: '0 0 9 ? * MON' },
        { label: '每月1号9点', value: '0 0 9 1 * ?' },
        { label: '每小时', value: '0 0 * * * ?' },
        { label: '每30分钟', value: '0 */30 * * * ?' }
    ];

    // Cron表达式校验函数
    const validateCronExpression = (cronExp) => {
        if (!cronExp || !cronExp.trim()) {
            return 'Cron表达式不能为空';
        }

        const parts = cronExp.trim().split(/\s+/);
        if (parts.length !== 6) {
            return 'Cron表达式必须包含6个字段：秒 分 时 日 月 周';
        }

        const [second, minute, hour, day, month, week] = parts;

        if (!isValidCronField(second, 0, 59)) {
            return '秒字段无效，应为0-59或*或*/n格式';
        }

        if (!isValidCronField(minute, 0, 59)) {
            return '分钟字段无效，应为0-59或*或*/n格式';
        }

        if (!isValidCronField(hour, 0, 23)) {
            return '小时字段无效，应为0-23或*或*/n格式';
        }

        if (day !== '?' && !isValidCronField(day, 1, 31)) {
            return '日期字段无效，应为1-31或?或*或*/n格式';
        }

        if (!isValidCronField(month, 1, 12)) {
            return '月份字段无效，应为1-12或*或*/n格式';
        }

        if (week !== '?' && !isValidWeekField(week)) {
            return '周字段无效，应为1-7或MON-SUN或?或*格式';
        }

        if (day !== '?' && week !== '?') {
            return '日期和周字段不能同时指定，其中一个必须为?';
        }

        return null;
    };

    // 校验Cron字段的辅助函数
    const isValidCronField = (field, min, max) => {
        if (field === '*') return true;
        if (field === '?') return true;

        if (field.includes('*/')) {
            const step = parseInt(field.split('*/')[1]);
            return !isNaN(step) && step > 0 && step <= max;
        }

        if (field.includes('-')) {
            const [start, end] = field.split('-').map(n => parseInt(n));
            return !isNaN(start) && !isNaN(end) && start >= min && end <= max && start <= end;
        }

        if (field.includes(',')) {
            const values = field.split(',').map(n => parseInt(n));
            return values.every(v => !isNaN(v) && v >= min && v <= max);
        }

        const num = parseInt(field);
        return !isNaN(num) && num >= min && num <= max;
    };

    // 校验周字段的辅助函数
    const isValidWeekField = (field) => {
        if (field === '*' || field === '?') return true;

        const weekNames = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

        if (weekNames.includes(field.toUpperCase())) return true;

        const num = parseInt(field);
        if (!isNaN(num) && num >= 1 && num <= 7) return true;

        if (field.includes('-') || field.includes(',')) {
            return field.split(/[-,]/).every(part => {
                const trimmed = part.trim();
                return weekNames.includes(trimmed.toUpperCase()) ||
                       (!isNaN(parseInt(trimmed)) && parseInt(trimmed) >= 1 && parseInt(trimmed) <= 7);
            });
        }

        return false;
    };

    // 渲染任务详情
    const renderTaskDetail = () => {
        if (!selectedTask) {
            return (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <svg className="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                    </svg>
                    <p>选择一个任务查看详情</p>
                </div>
            );
        }

        return (
            <div className="h-full flex flex-col">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-semibold">{selectedTask.title}</h3>
                    <div className="flex space-x-2">
                        <button
                            onClick={handleEdit}
                            className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
                        >
                            编辑
                        </button>
                        <button
                            onClick={handleDelete}
                            className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 text-sm"
                        >
                            删除
                        </button>
                    </div>
                </div>

                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">周期类型:</label>
                        <p className="text-gray-600">
                            {selectedTask.cycleType === 'day' ? '日' :
                             selectedTask.cycleType === 'week' ? '周' :
                             selectedTask.cycleType === 'month' ? '月' :
                             selectedTask.cycleType || '未设置'}
                        </p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">任务标题:</label>
                        <p className="text-gray-600 font-medium">{selectedTask.title}</p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">选择的参数:</label>
                        {selectedTask.methodId ? (
                            <div className="bg-gray-50 p-3 rounded-md">
                                {(() => {
                                    const selectedMethod = toolsData.find(method => method.id == selectedTask.methodId);
                                    return selectedMethod ? (
                                        <div>
                                            <p className="text-sm text-gray-600">
                                                {selectedMethod.name} - {selectedMethod.invokeType}
                                            </p>
                                            <p className="text-xs text-gray-500 mt-1">ID: {selectedTask.methodId}</p>
                                        </div>
                                    ) : (
                                        <p className="text-sm text-gray-600">{selectedTask.methodId}</p>
                                    );
                                })()}
                            </div>
                        ) : (
                            <p className="text-gray-600">未选择工具</p>
                        )}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">业务类型:</label>
                        <p className="text-gray-600">
                            {selectedTask.bizType === 'deliver' ? '交付' :
                             selectedTask.bizType === 'sale' ? '销售' :
                             selectedTask.bizType === 'customer_service' ? '客户服务' :
                             selectedTask.bizType || '未设置'}
                        </p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">关联模板:</label>
                        {selectedTask.templateId ? (
                            <div className="space-y-1">
                                {selectedTask.templateId.split(',').map(id => id.trim()).filter(id => id).map(templateId => {
                                    const template = templates.find(t => t.id.toString() === templateId);
                                    return (
                                        <div key={templateId} className="bg-blue-50 px-2 py-1 rounded text-sm text-blue-700 inline-block mr-2 mb-1">
                                            {template ? template.title : `未知模板(${templateId})`}
                                        </div>
                                    );
                                })}
                            </div>
                        ) : (
                            <p className="text-gray-600">未选择模板</p>
                        )}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Cron表达式:</label>
                        <p className="text-gray-600 font-mono">{selectedTask.cornExp}</p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">数据保留设置:</label>
                        <p className="text-gray-600">
                            {selectedTask.reten === 1 ? '保留同一周期的数据' : '不保留同一周期的数据'}
                        </p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">覆盖重复设置:</label>
                        <p className="text-gray-600">{selectedTask.coverRepeat || 0}</p>
                    </div>

                    <div className="text-sm text-gray-500">
                        <p>创建于: {selectedTask.createdAt}</p>
                        <p>更新于: {selectedTask.updatedAt}</p>
                    </div>
                </div>
            </div>
        );
    };

    // 渲染创建/编辑表单
    const renderTaskForm = () => (
        <div className="h-full flex flex-col">
            <h3 className="text-xl font-semibold mb-4">{selectedTask ? '编辑任务' : '创建新任务'}</h3>
            <div className="space-y-4 flex-1">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">周期类型</label>
                    <select
                        name="cycleType"
                        value={formData.cycleType}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="day">日</option>
                        <option value="week">周</option>
                        <option value="month">月</option>
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">任务标题 *</label>
                    <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入任务标题"
                    />
                </div>

                {/* 参数选择区域 */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">选择参数</label>
                    <select
                        value={selectedMethodId}
                        onChange={(e) => handleMethodChange(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        disabled={toolsLoading}
                    >
                        <option value="">请选择参数</option>
                        {toolsData.map(method => (
                            <option key={method.id} value={method.id}>
                                {method.name} - {method.invokeType}
                            </option>
                        ))}
                    </select>
                    {toolsLoading && (
                        <p className="text-sm text-gray-500 mt-1">加载参数列表中...</p>
                    )}
                    {selectedMethodId && (
                        <div className="mt-2 p-3 bg-blue-50 rounded-md border border-blue-200">
                            {(() => {
                                const selectedMethod = toolsData.find(method => method.id == selectedMethodId);
                                return selectedMethod ? (
                                    <div>
                                        <p className="text-sm text-blue-700">
                                            <strong>已选择参数:</strong> {selectedMethod.name}
                                        </p>
                                        <p className="text-xs text-blue-600 mt-1">类型: {selectedMethod.invokeType}</p>
                                        <p className="text-xs text-blue-600">ID: {selectedMethodId}</p>
                                    </div>
                                ) : (
                                    <p className="text-sm text-blue-700">
                                        <strong>已选择参数ID:</strong> {selectedMethodId}
                                    </p>
                                );
                            })()
                        }
                        </div>
                    )}
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">业务类型 *</label>
                    <select
                        name="bizType"
                        value={formData.bizType}
                        onChange={(e) => handleBizTypeChange(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="">请选择业务类型</option>
                        <option value="deliver">交付</option>
                        <option value="sale">销售</option>
                        <option value="customer_service">客户服务</option>
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">关联模板 * (可多选)</label>
                    {!formData.bizType ? (
                        <p className="text-sm text-yellow-600 mt-1">请先选择业务类型</p>
                    ) : templatesLoading ? (
                        <p className="text-sm text-gray-500 mt-1">加载模板列表中...</p>
                    ) : templates.length === 0 ? (
                        <p className="text-sm text-gray-500 mt-1">该业务类型下暂无可用模板</p>
                    ) : (
                        <div className="space-y-2">
                            <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
                                {templates.map(template => (
                                    <label key={template.id} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                                        <input
                                            type="checkbox"
                                            checked={selectedTemplateIds.includes(template.id.toString())}
                                            onChange={() => handleTemplateChange(template.id.toString())}
                                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        />
                                        <span className="text-sm">
                                            <span className="font-medium">{template.title}</span>
                                            {template.remark && <span className="text-gray-500"> - {template.remark}</span>}
                                        </span>
                                    </label>
                                ))}
                            </div>
                            {selectedTemplateIds.length > 0 && (
                                <div className="mt-2">
                                    <p className="text-sm text-gray-600 mb-1">已选择 {selectedTemplateIds.length} 个模板:</p>
                                    <div className="flex flex-wrap gap-1">
                                        {selectedTemplateIds.map(templateId => {
                                            const template = templates.find(t => t.id.toString() === templateId);
                                            return (
                                                <span key={templateId} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                                    {template ? template.title : `模板${templateId}`}
                                                </span>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Cron表达式 *</label>
                    <div className="space-y-3">
                        {/* Cron表达式输入 */}
                        <div>
                            <input
                                type="text"
                                name="cornExp"
                                value={formData.cornExp}
                                onChange={handleInputChange}
                                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 font-mono ${
                                    cronValidationError
                                        ? 'border-red-300 focus:ring-red-500'
                                        : 'border-gray-300 focus:ring-blue-500'
                                }`}
                                placeholder="输入Cron表达式"
                            />
                            {cronValidationError && (
                                <p className="text-red-500 text-xs mt-1">{cronValidationError}</p>
                            )}
                        </div>

                        {/* 预设按钮 */}
                        <div className="flex flex-wrap gap-2">
                            {cronPresets.map((preset, index) => (
                                <button
                                    key={index}
                                    type="button"
                                    onClick={() => {
                                        setFormData(prev => ({ ...prev, cornExp: preset.value }));
                                        setCronValidationError('');
                                    }}
                                    className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                                >
                                    {preset.label}
                                </button>
                            ))}
                        </div>

                        <p className="text-xs text-gray-500">
                            Cron表达式格式: 秒 分 时 日 月 周 (例: 0 0 9 * * ? 表示每天9点执行)
                        </p>
                    </div>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">数据保留设置</label>
                    <select
                        name="reten"
                        value={formData.reten}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value={0}>不保留同一周期的数据</option>
                        <option value={1}>保留同一周期的数据</option>
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">覆盖重复设置</label>
                    <input
                        type="number"
                        name="coverRepeat"
                        value={formData.coverRepeat}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="输入数字（默认0）"
                        min="0"
                    />
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                    <button
                        onClick={handleCancel}
                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                        取消
                    </button>
                    <button
                        onClick={handleSave}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                    >
                        保存
                    </button>
                </div>
            </div>
        </div>
    );

    // 主渲染函数
    return (
        <div className="h-full flex flex-col text-gray-800">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">任务管理</h2>
                {!isEditing && (
                    <button
                        onClick={handleCreateNew}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                        创建新任务
                    </button>
                )}
            </div>

            {loading ? (
                <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
            ) : (
                <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-6 h-full">
                    {/* 任务列表 */}
                    <div className="bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden">
                        <div className="p-4 border-b border-gray-200">
                            <h3 className="font-medium">任务列表</h3>
                        </div>
                        <div className="overflow-y-auto" style={{ maxHeight: 'calc(100vh - 280px)' }}>
                            {tasks.length === 0 ? (
                                <div className="p-4 text-center text-gray-500">
                                    暂无任务，点击"创建新任务"按钮添加
                                </div>
                            ) : (
                                <ul className="divide-y divide-gray-200">
                                    {tasks.map(task => (
                                        <li
                                            key={task.id}
                                            className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${selectedTask && selectedTask.id === task.id ? 'bg-blue-50' : ''}`}
                                            onClick={() => handleSelectTask(task)}
                                        >
                                            <div className="flex justify-between items-start">
                                                <div className="flex-1 min-w-0">
                                                    <h4 className="font-medium">{task.title}</h4>
                                                    <p className="text-sm text-gray-500 mt-1 truncate">{task.remark || '无描述'}</p>
                                                    <div className="text-xs text-gray-400 mt-1">
                                                        <p className="truncate">模板: {getTemplateName(task.templateId)}</p>
                                                        <p>更新于: {task.updatedAt}</p>
                                                    </div>
                                                </div>
                                                <button
                                                    onClick={(e) => handleCopyTask(task, e)}
                                                    className="ml-2 p-1.5 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded transition-colors flex-shrink-0"
                                                    title="复制任务"
                                                >
                                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </div>
                    </div>

                    {/* 任务详情及编辑区 */}
                    <div className="bg-white bg-opacity-80 rounded-xl shadow-sm overflow-hidden md:col-span-2 p-6">
                        {isEditing ? renderTaskForm() : renderTaskDetail()}
                    </div>
                </div>
            )}
        </div>
    );
}

// 导出组件
window.TaskManagementCrud = TaskManagementCrud;
