# 任务管理拆分功能实现

## 概述

根据需求，将原有的任务管理页面拆分成两个独立的页面：
1. **任务管理页面** - 专注于任务的增删改查维护
2. **任务结果页面** - 专注于查看任务执行记录和结果分析

同时实现了结果分析中的prompt编辑功能，支持编辑prompt并重新分析。

## 功能特性

### 1. 任务管理页面 (TaskManagementCrud)

**功能范围：**
- 任务的创建、编辑、删除
- 任务基本信息维护（标题、业务类型、关联模板、Cron表达式等）
- 模板多选功能
- Cron表达式校验和预设
- 数据源参数选择

**主要特点：**
- 简洁的三栏布局：任务列表 | 任务详情/编辑表单
- 实时Cron表达式校验
- 支持多模板关联
- 友好的表单验证

### 2. 任务结果页面 (TaskResults) - 优化版

**功能范围：**
- 按业务类型筛选任务
- **优化：任务选择改为下拉框，节省空间**
- **新增：Run和Debug按钮，支持直接执行任务**
- 查看任务执行记录
- 查看任务结果详情
- 重新分析功能
- **新增：弹框式Prompt编辑功能**

**主要特点：**
- **优化布局：业务类型和任务选择在同一行，右侧添加执行按钮**
- **简化为两栏布局：执行记录 | 结果详情**
- 实时刷新运行中的任务
- 分页浏览执行记录
- 支持批次重新分析
- **弹框式prompt编辑，支持实时查看分析效果**

### 3. Prompt编辑功能 - 弹框版

**新增功能：**
- 点击"编辑Prompt"按钮打开弹框
- 弹框中显示：提示词编辑区域、原始数据、分析结果
- 支持三种操作：保存、保存并重新分析、取消
- **保存并重新分析后弹框保持打开，便于迭代优化**

**弹框布局：**
- 第一行：提示词编辑区域（占用较大空间）
- 第二行：原始数据和分析结果左右布局
- 底部：操作按钮（保存、保存并重新分析、取消）

**实现细节：**
- 前端：弹框式编辑界面，支持实时查看效果
- 后端：新增`/task/updatePromptOnly`和`/task/updatePromptAndReanalyze`接口
- 数据库：通过远程接口更新prompt并重新分析

### 4. 任务执行功能

**新增功能：**
- Run按钮：执行正常任务
- Debug按钮：执行调试任务（默认1次）
- 执行状态显示和防重复点击
- 执行后自动刷新记录列表

## 技术实现

### 前端组件

1. **TaskManagementCrud.js** - 任务增删改查管理组件
   - 位置：`ai-backend-web/src/main/resources/static/js/components/TaskManagementCrud.js`
   - 功能：任务的基本维护操作

2. **TaskResults.js** - 任务结果查看组件
   - 位置：`ai-backend-web/src/main/resources/static/js/components/TaskResults.js`
   - 功能：任务结果查看和prompt编辑

### 后端接口

1. **新增接口：**
   ```java
   POST /task/updatePromptOnly
   ```
   - 功能：仅更新指定任务结果项的prompt
   - 参数：taskItemId, newPrompt
   - 返回：处理结果信息

   ```java
   POST /task/updatePromptAndReanalyze
   ```
   - 功能：更新指定任务结果项的prompt并重新分析
   - 参数：taskItemId, newPrompt, taskId, batchNo
   - 返回：处理结果信息

2. **新增BO类：**
   - `TaskResultItemUpdatePromptBo` - 用于更新prompt的参数对象
   - `UpdatePromptOnlyRequest` - 仅更新prompt的请求对象
   - `UpdatePromptAndReanalyzeRequest` - 更新prompt并重新分析的请求对象

3. **新增Feign接口：**
   ```java
   POST /result/updateItemPromptCommon.do
   ```
   - 功能：远程更新任务结果项的prompt字段

### 导航更新

更新了侧边栏导航：
- **模板管理** - 原有功能
- **任务管理** - 新的任务增删改查页面
- **任务结果** - 新的任务结果查看页面
- **方法注册** - 原有功能

## 使用流程

### 任务管理流程
1. 进入"任务管理"页面
2. 点击"创建新任务"或选择现有任务进行编辑
3. 填写任务信息（标题、业务类型、模板、Cron表达式等）
4. 保存任务

### 任务结果查看流程
1. 进入"任务结果"页面
2. 选择业务类型（交付/销售/客户服务）
3. **从下拉框中选择要查看的任务**
4. **可选：点击Run或Debug按钮执行任务**
5. 从执行记录列表中选择要查看的批次
6. 在结果详情中查看分析结果

### Prompt编辑流程（弹框版）
1. 在任务结果页面选择具体的执行记录
2. 在结果详情中找到要编辑的分析项
3. **点击"编辑Prompt"按钮打开弹框**
4. **在弹框中查看和编辑提示词**
5. **查看原始数据和当前分析结果**
6. **选择操作：**
   - **保存：仅保存prompt，关闭弹框**
   - **保存并重新分析：保存prompt并重新分析，弹框保持打开**
   - **取消：放弃修改，关闭弹框**
7. **重新分析后可继续在弹框中优化prompt**

## 数据流

### Prompt编辑数据流

**仅保存Prompt：**
1. 前端发送更新请求到 `/task/updatePromptOnly`
2. 后端TaskController接收请求并调用TaskResultService.updatePromptOnly()
3. TaskResultService通过远程接口更新prompt字段
4. 前端刷新结果详情并关闭弹框

**保存并重新分析：**
1. 前端发送更新请求到 `/task/updatePromptAndReanalyze`
2. 后端TaskController接收请求并调用TaskResultService.updatePromptAndReanalyze()
3. TaskResultService执行以下步骤：
   - 通过远程接口更新prompt字段
   - 获取分析方法配置
   - 使用新prompt和原始数据调用分析方法
   - 更新分析结果到itemResult字段
4. 前端刷新结果详情，弹框保持打开显示最新分析结果

### 任务执行数据流
1. 前端点击Run/Debug按钮
2. 调用 `/task/run/{taskId}` 或 `/task/debug/{taskId}/{count}` 接口
3. 后端执行任务并返回结果
4. 前端自动刷新执行记录列表
5. 启动定时器监控执行状态

## 注意事项

1. **兼容性**：保留了原有的TaskManagement组件，确保向后兼容
2. **权限**：新功能继承了原有的权限控制机制
3. **错误处理**：完善的错误处理和用户提示
4. **性能**：实时刷新机制避免了不必要的请求
5. **用户体验**：友好的加载状态和操作反馈

## 文件清单

### 新增文件
- `ai-backend-web/src/main/resources/static/js/components/TaskManagementCrud.js`
- `ai-backend-web/src/main/resources/static/js/components/TaskResults.js`
- `ai-backend-core/src/main/java/com/djcps/ai/core/vo/in/easyorder/TaskResultItemUpdatePromptBo.java`
- `docs/task_management_split.md`

### 修改文件
- `ai-backend-web/src/main/resources/static/js/components/Sidebar.js` - 更新导航菜单
- `ai-backend-web/src/main/resources/static/js/app.js` - 添加新页面路由
- `ai-backend-web/src/main/resources/static/index.html` - 引入新组件
- `ai-backend-web/src/main/resources/static/js/config/api.js` - 添加新API配置
- `ai-backend-web/src/main/java/com/djcps/ai/web/tools/TaskController.java` - 添加新接口和请求对象
- `ai-backend-service/src/main/java/com/djcps/ai/service/TaskResultService.java` - 添加新方法
- `ai-backend-core/src/main/java/com/djcps/ai/core/client/EasyOrderFeign.java` - 添加新Feign接口

## 测试建议

1. **功能测试**：
   - 测试任务的创建、编辑、删除功能
   - 测试任务结果的查看功能
   - 测试prompt编辑和重新分析功能

2. **界面测试**：
   - 测试页面布局和响应式设计
   - 测试加载状态和错误提示
   - 测试用户交互体验

3. **集成测试**：
   - 测试前后端接口集成
   - 测试远程服务调用
   - 测试数据一致性
